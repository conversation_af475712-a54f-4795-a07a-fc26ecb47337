# Manual Screening Implementation Guide

This document explains the manual screening functionality that integrates with your `vedaScreening.route.ts` endpoint.

## Overview

The manual screening implementation allows you to:
1. Conduct screening calls manually (outside the system)
2. Record the results (pass/fail) using your existing `vedaScreening.route.ts` endpoint
3. Update candidate status in the workflow

## API Integration

### Endpoint
- **Route:** `vedaScreening.route.ts`
- **Method:** `POST /veda-screening`

### Payload Format
```typescript
{
  candidateId: string,
  status: "CALL_COMPLETED",     // Always this value
  screeningCleared: "true" | "false"  // String, not boolean
}
```

### Example Requests

**Pass a candidate:**
```json
{
  "candidateId": "candidate_123",
  "status": "CALL_COMPLETED",
  "screeningCleared": "true"
}
```

**Fail a candidate:**
```json
{
  "candidateId": "candidate_456", 
  "status": "CALL_COMPLETED",
  "screeningCleared": "false"
}
```

## Implementation Files

### 1. Hook: `src/hooks/useManualScreening.ts`

**Main Functions:**
- `useManualScreening()` - Core hook for API calls
- `usePassManualScreening()` - Hook for passing candidates
- `useFailManualScreening()` - Hook for failing candidates  
- `useManualScreeningActions()` - Combined hook for both actions

**Usage:**
```typescript
import { useManualScreeningActions } from '@/hooks/useManualScreening'

const { passCandidate, failCandidate, isLoading } = useManualScreeningActions()

// Pass a candidate
await passCandidate('candidate_123')

// Fail a candidate  
await failCandidate('candidate_456')
```

### 2. UI Components: `src/components/screening/ManualScreeningActions.tsx`

**Components:**
- `ManualScreeningActions` - Full UI with call instructions and pass/fail buttons
- `QuickManualScreeningActions` - Compact pass/fail buttons only

**Variants:**
- `default` - Full buttons with confirmation dialogs
- `compact` - Small buttons for table rows
- `dropdown` - For dropdown menus (future enhancement)

**Usage:**
```typescript
import { ManualScreeningActions } from '@/components/screening/ManualScreeningActions'

// Full component
<ManualScreeningActions 
  candidate={candidate}
  showCallButton={true}
  variant="default"
/>

// Compact for tables
<ManualScreeningActions 
  candidate={candidate}
  variant="compact"
  showCallButton={false}
/>
```

## Integration Examples

### 1. In Candidate List/Table

```typescript
import { ManualScreeningActions } from '@/components/screening/ManualScreeningActions'

function CandidateRow({ candidate }) {
  return (
    <tr>
      <td>{candidate.name}</td>
      <td>{candidate.phone}</td>
      <td>
        <ManualScreeningActions 
          candidate={candidate}
          variant="compact"
        />
      </td>
    </tr>
  )
}
```

### 2. In Candidate Detail View

```typescript
import { ManualScreeningActions } from '@/components/screening/ManualScreeningActions'

function CandidateDetail({ candidate }) {
  return (
    <div>
      <h2>{candidate.name}</h2>
      
      {candidate.stage === 'screening' && (
        <ManualScreeningActions 
          candidate={candidate}
          showCallButton={true}
          variant="default"
        />
      )}
    </div>
  )
}
```

### 3. Quick Actions

```typescript
import { QuickManualScreeningActions } from '@/components/screening/ManualScreeningActions'

function QuickActions({ candidate }) {
  return (
    <QuickManualScreeningActions 
      candidate={candidate}
      disabled={candidate.stage !== 'screening'}
    />
  )
}
```

## Workflow Integration

### Current Status
✅ **Completed:**
- Manual screening hooks with proper API integration
- UI components for pass/fail actions
- Confirmation dialogs and loading states
- Toast notifications for success/error
- Query invalidation for data refresh

### Integration Points

1. **Candidate Management Pages**
   - Add manual screening buttons to candidate lists
   - Include in candidate detail views
   - Integrate with existing workflow status

2. **Workflow Dashboard**
   - Show manual screening options for candidates in screening stage
   - Display screening status and results

3. **Bulk Operations**
   - Allow bulk pass/fail for multiple candidates
   - Export screening results

## Comparison: Automated vs Manual Screening

| Feature | Automated Screening | Manual Screening |
|---------|-------------------|------------------|
| **Endpoint** | `/trigger-screening-call` | `/veda-screening` |
| **Trigger** | System initiates call | Human conducts call |
| **Questions** | From job configuration | Manual/flexible |
| **Recording** | Automatic | Manual result entry |
| **Payload** | questions, companyName, role, to | candidateId, status, screeningCleared |
| **Use Case** | High volume, standardized | Custom questions, complex scenarios |

## Next Steps

1. **Add to Existing Pages:**
   - Integrate `ManualScreeningActions` into your candidate list components
   - Add to candidate detail views
   - Include in workflow dashboards

2. **Enhance UI:**
   - Add bulk screening actions
   - Create screening history/audit trail
   - Add screening notes/comments

3. **Workflow Integration:**
   - Connect with your existing candidate stage management
   - Add screening analytics and reporting
   - Integrate with notification systems

## Example Integration

See `src/examples/CompleteScreeningExample.tsx` for a comprehensive example showing both automated and manual screening in action.

The implementation is ready to use - just import the components and hooks into your existing candidate management interfaces!
