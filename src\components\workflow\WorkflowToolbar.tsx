import { useState, useRef } from 'react'
import { Plus, Save, Upload, Download, Play, AlertTriangle, CheckCircle, FileText, Trash2, <PERSON><PERSON>, Zap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useWorkflowStore, defaultStages } from '@/stores/workflowStore'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'
import { useCreateJobConfig } from '@/hooks/useJobConfig'

interface WorkflowToolbarProps {
  jobId?: string
}

export function WorkflowToolbar({ jobId }: WorkflowToolbarProps) {
  const {
    addStageNode,
    saveWorkflow,
    loadWorkflow,
    createNewWorkflow,
    validateWorkflow,
    generateJobConfig,
    nodes,
    edges
  } = useWorkflowStore()

  const { toast } = useToast()
  const createJobConfigMutation = useCreateJobConfig()
  const [isSaving, setIsSaving] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [showJobConfigDialog, setShowJobConfigDialog] = useState(false)
  const [generatedJobConfig, setGeneratedJobConfig] = useState<any>(null)
  const toolbarRef = useRef<HTMLDivElement>(null)

  // Handle adding a new stage node
  const handleAddStage = (stageId: string) => {
    // Calculate position for new node (simple grid layout)
    const existingNodes = nodes.length
    const x = 200 + (existingNodes % 4) * 250
    const y = 100 + Math.floor(existingNodes / 4) * 200

    addStageNode(stageId, { x, y })

    toast({
      title: "Stage Added",
      description: `${stageId} stage has been added to the workflow.`,
    })
  }

  // Handle saving workflow
  const handleSave = async () => {
    if (!jobId) {
      toast({
        title: "Save Failed",
        description: "No job selected. Please select a job first.",
        variant: "destructive",
      })
      return
    }

    setIsSaving(true)
    try {
      const validation = validateWorkflow()

      if (!validation.isValid) {
        toast({
          title: "Validation Failed",
          description: validation.errors.join(', '),
          variant: "destructive",
        })
        return
      }

      // Generate JobConfig object
      const jobConfig = generateJobConfig(jobId)

      // Display the generated JobConfig in console
      console.log('Generated JobConfig:', JSON.stringify(jobConfig, null, 2))

      // Store the config and show dialog
      setGeneratedJobConfig(jobConfig)
      setShowJobConfigDialog(true)

      // In a real app, this would save to the backend
      // await saveJobConfigToAPI(jobConfig)

      toast({
        title: "Workflow Saved Successfully! 🎉",
        description: `Generated JobConfig with ${jobConfig.flow.length} stages and ${jobConfig.stageConfig.length} configurations.`,
      })

    } catch (error) {
      console.error('Save workflow error:', error)
      toast({
        title: "Save Failed",
        description: "Failed to generate workflow configuration.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Handle creating new workflow
  const handleNewWorkflow = () => {
    if (jobId) {
      createNewWorkflow(jobId)
      toast({
        title: "New Workflow",
        description: "Created a new workflow. Start by adding stages.",
      })
    }
  }

  // Handle workflow validation
  const handleValidate = () => {
    const validation = validateWorkflow()

    if (validation.isValid) {
      toast({
        title: "Validation Passed",
        description: "Your workflow is valid and ready to use.",
      })
    } else {
      toast({
        title: "Validation Issues",
        description: validation.errors.join(', '),
        variant: "destructive",
      })
    }
  }

  // Handle clear workflow
  const handleClear = () => {
    if (jobId) {
      createNewWorkflow(jobId)
      toast({
        title: "Workflow Cleared",
        description: "The workflow has been cleared.",
      })
    }
  }

  // Handle copy JobConfig to clipboard
  const handleCopyJobConfig = async () => {
    if (generatedJobConfig) {
      try {
        await navigator.clipboard.writeText(JSON.stringify(generatedJobConfig, null, 2))
        toast({
          title: "Copied to Clipboard",
          description: "JobConfig JSON has been copied to your clipboard.",
        })
      } catch (error) {
        toast({
          title: "Copy Failed",
          description: "Failed to copy to clipboard.",
          variant: "destructive",
        })
      }
    }
  }

  // Handle activating workflow (POST to backend)
  const handleActivate = async () => {
    if (!jobId) {
      toast({
        title: "Activation Failed",
        description: "No job selected. Please select a job first.",
        variant: "destructive",
      })
      return
    }

    try {
      const validation = validateWorkflow()

      if (!validation.isValid) {
        toast({
          title: "Validation Failed",
          description: validation.errors.join(', '),
          variant: "destructive",
        })
        return
      }

      // Generate JobConfig object
      const jobConfig = generateJobConfig(jobId)

      // Prepare data for backend with proper structure (let MongoDB auto-generate _id)
      const activationData = {
        jobId: jobId,
        flow: jobConfig.flow,
        stageConfig: jobConfig.stageConfig
      }

      // Use mutation hook to POST to backend
      await createJobConfigMutation.mutateAsync(activationData)

      toast({
        title: "Workflow Activated Successfully! 🚀",
        description: `Workflow has been activated for job ${jobId} with ${jobConfig.flow.length} stages.`,
      })

    } catch (error) {
      console.error('Activate workflow error:', error)
      toast({
        title: "Activation Failed",
        description: "Failed to activate workflow. Please try again.",
        variant: "destructive",
      })
    }
  }



  // Handle mouse events to prevent glitching
  const handleMouseEnter = () => {
    if (!isDropdownOpen) {
      setIsExpanded(true)
    }
  }

  const handleMouseLeave = () => {
    if (!isDropdownOpen) {
      setIsExpanded(false)
    }
  }

  // Get validation status
  const validation = validateWorkflow()

  // Toolbar items configuration
  const toolbarItems = [
    {
      id: 'new',
      icon: FileText,
      label: 'New Workflow',
      onClick: handleNewWorkflow,
      disabled: !jobId,
    },
    {
      id: 'save',
      icon: Save,
      label: isSaving ? 'Saving...' : 'Save Workflow',
      onClick: handleSave,
      disabled: isSaving || nodes.length === 0,
    },
    {
      id: 'activate',
      icon: Zap,
      label: createJobConfigMutation.isPending ? 'Activating...' : 'Activate Workflow',
      onClick: handleActivate,
      disabled: createJobConfigMutation.isPending || !validation.isValid || nodes.length === 0 || !jobId,
      variant: 'primary',
    },
    {
      id: 'validate',
      icon: validation.isValid ? CheckCircle : AlertTriangle,
      label: 'Validate Workflow',
      onClick: handleValidate,
      disabled: nodes.length === 0,
      variant: validation.isValid ? 'success' : 'warning',
    },
    {
      id: 'clear',
      icon: Trash2,
      label: 'Clear Workflow',
      onClick: handleClear,
      disabled: !jobId || nodes.length === 0,
      variant: 'destructive',
    },
  ]

  return (
    <TooltipProvider>
      <div
        ref={toolbarRef}
        className={cn(
          "absolute left-4 top-1/2 -translate-y-1/2 z-40 bg-white rounded-lg shadow-lg border border-gray-200 transition-all duration-300 ease-in-out",
          isExpanded ? "w-64" : "w-12"
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className="p-2 space-y-2">
          {/* Main Action Buttons */}
          {toolbarItems.map((item) => (
            <Tooltip key={item.id}>
              <TooltipTrigger asChild>
                <Button
                  variant={
                    item.variant === 'destructive' ? 'destructive' :
                    item.variant === 'primary' ? 'default' : 'ghost'
                  }
                  size="sm"
                  onClick={item.onClick}
                  disabled={item.disabled}
                  className={cn(
                    "w-full justify-start h-8 transition-all duration-200",
                    !isExpanded && "w-8 px-0 justify-center",
                    item.variant === 'success' && "text-green-600 hover:text-green-700 hover:bg-green-50",
                    item.variant === 'warning' && "text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50",
                    item.variant === 'primary' && "bg-blue-600 hover:bg-blue-700 text-white"
                  )}
                >
                  <item.icon className={cn("h-4 w-4", isExpanded && "mr-2")} />
                  {isExpanded && (
                    <span className="text-sm font-medium">{item.label}</span>
                  )}
                </Button>
              </TooltipTrigger>
              {!isExpanded && (
                <TooltipContent side="right">
                  <p>{item.label}</p>
                </TooltipContent>
              )}
            </Tooltip>
          ))}

          {/* Separator */}
          <div className="border-t border-gray-200 my-2" />

          {/* Add Stage Dropdown */}
          <DropdownMenu
            open={isDropdownOpen}
            onOpenChange={(open) => {
              setIsDropdownOpen(open)
              if (open) {
                setIsExpanded(true)
              }
            }}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "w-full justify-start h-8 transition-all duration-200",
                      !isExpanded && "w-8 px-0 justify-center"
                    )}
                  >
                    <Plus className={cn("h-4 w-4", isExpanded && "mr-2")} />
                    {isExpanded && (
                      <span className="text-sm font-medium">Add Stage</span>
                    )}
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              {!isExpanded && (
                <TooltipContent side="right">
                  <p>Add Stage</p>
                </TooltipContent>
              )}
            </Tooltip>
            <DropdownMenuContent side="right" className="w-56">
              {defaultStages.map((stage) => (
                <DropdownMenuItem
                  key={stage.id}
                  onClick={() => handleAddStage(stage.id)}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: stage.color }}
                    />
                    <div>
                      <div className="font-medium">{stage.label}</div>
                      <div className="text-xs text-gray-500">{stage.id}</div>
                    </div>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Import/Export Dropdown */}
          <DropdownMenu
            onOpenChange={(open) => {
              if (open) {
                setIsExpanded(true)
              }
            }}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "w-full justify-start h-8 transition-all duration-200",
                      !isExpanded && "w-8 px-0 justify-center"
                    )}
                  >
                    <Upload className={cn("h-4 w-4", isExpanded && "mr-2")} />
                    {isExpanded && (
                      <span className="text-sm font-medium">Import/Export</span>
                    )}
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              {!isExpanded && (
                <TooltipContent side="right">
                  <p>Import/Export</p>
                </TooltipContent>
              )}
            </Tooltip>
            <DropdownMenuContent side="right">
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                Import from File
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export Workflow
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <FileText className="h-4 w-4 mr-2" />
                Load Template
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* JobConfig Display Dialog */}
      <Dialog open={showJobConfigDialog} onOpenChange={setShowJobConfigDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>Generated JobConfig</span>
            </DialogTitle>
            <DialogDescription>
              Your workflow has been converted to a JobConfig object. This configuration defines the flow logic and stage parameters.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Summary */}
            {generatedJobConfig && (
              <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{generatedJobConfig.flow?.length || 0}</div>
                  <div className="text-sm text-gray-600">Flow Stages</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{generatedJobConfig.stageConfig?.length || 0}</div>
                  <div className="text-sm text-gray-600">Stage Configs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{generatedJobConfig.jobId || 'N/A'}</div>
                  <div className="text-sm text-gray-600">Job ID</div>
                </div>
              </div>
            )}

            {/* JSON Display */}
            <div className="relative">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium">JobConfig JSON:</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyJobConfig}
                  className="flex items-center space-x-1"
                >
                  <Copy className="h-4 w-4" />
                  <span>Copy</span>
                </Button>
              </div>
              <pre className="bg-gray-900 text-green-400 p-4 rounded-lg text-xs overflow-auto max-h-96 font-mono">
                {generatedJobConfig ? JSON.stringify(generatedJobConfig, null, 2) : 'No config generated'}
              </pre>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setShowJobConfigDialog(false)}>
                Close
              </Button>
              <Button onClick={handleCopyJobConfig}>
                <Copy className="h-4 w-4 mr-2" />
                Copy to Clipboard
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  )
}
