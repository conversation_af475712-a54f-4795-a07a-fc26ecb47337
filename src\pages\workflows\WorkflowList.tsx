import { useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Plus, Edit, Eye, Trash2, Play, Settings, Briefcase, Calendar, CheckCircle, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/tables/DataTable'
import { useJobs } from '@/hooks/useJobs'
import { useJobConfigs, useDeleteJobConfig } from '@/hooks/useJobConfig'
import { useAuth } from '@/contexts/AuthContext'
import { formatDate } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'

export function WorkflowList() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const { user } = useAuth()
  const { data: jobs, isLoading: jobsLoading } = useJobs()
  const { data: jobConfigs, isLoading: configsLoading } = useJobConfigs()
  const deleteJobConfigMutation = useDeleteJobConfig()

  // Filter jobs by user's organization and create workflow data
  const workflows = useMemo(() => {
    if (!jobs || !jobConfigs || !user) return []

    // Filter jobs by user's organization
    // user.organization is an object with _id, so we compare with user.organization._id
    const organizationJobs = jobs.filter(job => {
      const jobOrgId = typeof job.organization === 'string' ? job.organization : job.organization.toString()
      const userOrgId = typeof user.organization === 'string' ? user.organization : user.organization._id
      return jobOrgId === userOrgId
    })

    // Create workflow data by combining jobs and their configurations
    const result = organizationJobs.map(job => {
      const jobConfig = jobConfigs.find(config => config.jobId === job._id)

      if (jobConfig) {
        // Job has a configuration
        return {
          _id: jobConfig._id,
          jobId: job._id,
          name: `${job.title} Workflow`,
          status: 'active' as const,
          stagesCount: jobConfig.stageConfig.length,
          connectionsCount: jobConfig.flow.length,
          configuredStages: jobConfig.stageConfig.length,
          lastModified: new Date(jobConfig.updatedAt || jobConfig.createdAt),
          createdAt: new Date(jobConfig.createdAt),
          isValid: jobConfig.stageConfig.length > 0 && jobConfig.flow.length > 0,
          hasConfig: true,
          job: job
        }
      } else {
        // Job doesn't have a configuration yet
        return {
          _id: `no-config-${job._id}`,
          jobId: job._id,
          name: `${job.title} Workflow`,
          status: 'draft' as const,
          stagesCount: 0,
          connectionsCount: 0,
          configuredStages: 0,
          lastModified: new Date(job.updatedAt || job.createdAt),
          createdAt: new Date(job.createdAt),
          isValid: false,
          hasConfig: false,
          job: job
        }
      }
    })

    return result
  }, [jobs, jobConfigs, user])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleEdit = (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)
    if (workflow) {
      navigate(`/workflows/editor?jobId=${workflow.jobId}`)
    }
  }

  const handleView = (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)
    if (workflow) {
      if (workflow.hasConfig) {
        // Navigate to detailed workflow view
        navigate(`/workflows/view/${workflow.jobId}`)
      } else {
        // Navigate to editor to create workflow
        navigate(`/workflows/editor?jobId=${workflow.jobId}`)
      }
    }
  }

  const handleDelete = async (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)
    if (workflow && workflow.hasConfig) {
      try {
        await deleteJobConfigMutation.mutateAsync(workflow.jobId)
        toast({
          title: "Workflow Deleted",
          description: `Workflow for ${workflow.job.title} has been deleted.`,
        })
      } catch (error) {
        toast({
          title: "Delete Failed",
          description: "Failed to delete workflow configuration.",
          variant: "destructive",
        })
      }
    }
  }

  const handleCreateNew = () => {
    navigate('/workflows/editor')
  }

  const columns = [
    {
      key: 'name',
      label: 'Workflow',
      sortable: true,
      render: (value: string, row: any) => (
        <div className="min-w-[250px]">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${
                row.hasConfig
                  ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                  : 'bg-gradient-to-br from-gray-400 to-gray-500'
              }`}>
                <Settings className="h-5 w-5 text-white" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-sm font-semibold text-gray-900 truncate">{value}</div>
              <div className="text-xs text-gray-500 flex items-center space-x-1 mt-1">
                <Briefcase className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{row.job.title}</span>
              </div>
              <div className="text-xs text-gray-500 mt-0.5">
                {row.job.department} • {row.job.location}
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string, row: any) => (
        <div className="min-w-[120px]">
          <div className="flex items-center space-x-2">
            <Badge className={`${getStatusColor(value)} border-0 text-xs px-2 py-1`}>
              {value.toUpperCase()}
            </Badge>
            {row.isValid ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            )}
          </div>
        </div>
      )
    },
    {
      key: 'stagesCount',
      label: 'Stages',
      sortable: true,
      render: (value: number, row: any) => (
        <div className="min-w-[100px]">
          <div className="text-sm font-medium text-gray-900">{value}</div>
          <div className="text-xs text-gray-500">
            {row.configuredStages}/{value} configured
          </div>
        </div>
      )
    },
    {
      key: 'connectionsCount',
      label: 'Connections',
      sortable: true,
      render: (value: number) => (
        <div className="min-w-[100px]">
          <span className="text-sm font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    {
      key: 'lastModified',
      label: 'Last Modified',
      sortable: true,
      render: (value: Date) => (
        <div className="min-w-[120px]">
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-600">{formatDate(value)}</span>
          </div>
        </div>
      )
    },
  ]

  // Calculate stats
  const totalWorkflows = workflows.length
  const activeWorkflows = workflows.filter(w => w.status === 'active').length
  const draftWorkflows = workflows.filter(w => w.status === 'draft').length
  const validWorkflows = workflows.filter(w => w.isValid).length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Workflow Management</h1>
          <p className="text-gray-600">Create and manage recruitment workflows for your jobs</p>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Create Workflow
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalWorkflows}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeWorkflows}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <Edit className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{draftWorkflows}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valid</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{validWorkflows}</div>
          </CardContent>
        </Card>
      </div>

      {/* Workflows Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <DataTable
          data={workflows}
          columns={columns}
          loading={jobsLoading || configsLoading}
          onEdit={handleEdit}
          onView={handleView}
          onDelete={handleDelete}
        />
      </div>
    </div>
  )
}
