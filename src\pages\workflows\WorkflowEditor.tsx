import { useState, useEffect } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { <PERSON>Lef<PERSON>, Settings, Save, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { WorkflowCanvas } from '@/components/workflow/WorkflowCanvas'
import { StageConfigPanel } from '@/components/workflow/StageConfigPanel'
import { JobConfigManager } from '@/components/workflow/JobConfigManager'
import { useWorkflowStore } from '@/stores/workflowStore'
import { useJobs } from '@/hooks/useJobs'
import { useJobConfig, useSaveJobConfig } from '@/hooks/useJobConfig'
import { useToast } from '@/hooks/use-toast'

export function WorkflowEditor() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const jobId = searchParams.get('jobId')
  const [activeTab, setActiveTab] = useState<'editor' | 'manager'>('editor')

  const { data: jobs, isLoading: jobsLoading } = useJobs()
  const { data: jobConfig, isLoading: configLoading } = useJobConfig(jobId || '')
  const { toast } = useToast()
  const saveJobConfigMutation = useSaveJobConfig()

  const {
    loadWorkflow,
    createNewWorkflow,
    saveWorkflow,
    validateWorkflow,
    nodes,
    edges
  } = useWorkflowStore()

  // Load workflow configuration for the selected job
  useEffect(() => {
    if (jobId && jobs && !configLoading) {
      const job = jobs.find(j => j._id === jobId)
      console.log('WorkflowEditor useEffect:', { jobId, job: !!job, jobConfig, configLoading })

      if (job) {
        if (jobConfig && jobConfig !== null) {
          console.log('Loading existing job configuration:', jobConfig)
          // Load existing job configuration
          loadWorkflow(jobConfig)
        } else {
          // No configuration exists, create a new blank workflow
          console.log('No job configuration found, creating new workflow for job:', jobId)
          createNewWorkflow(jobId)
        }
      } else {
        console.log('Job not found for jobId:', jobId)
      }
    }
  }, [jobId, jobs, jobConfig, configLoading, loadWorkflow, createNewWorkflow])

  // Handle creating new workflow
  const handleNewWorkflow = () => {
    if (jobId) {
      createNewWorkflow(jobId)
      toast({
        title: "New Workflow Created",
        description: "Start building your workflow by adding stages.",
      })
    }
  }

  // Handle saving workflow
  const handleSave = async () => {
    if (!jobId) {
      toast({
        title: "Error",
        description: "No job selected for saving workflow.",
        variant: "destructive",
      })
      return
    }

    const validation = validateWorkflow()

    if (!validation.isValid) {
      toast({
        title: "Validation Failed",
        description: validation.errors.join(', '),
        variant: "destructive",
      })
      return
    }

    const savedConfig = saveWorkflow()
    if (!savedConfig) {
      toast({
        title: "Error",
        description: "Failed to prepare workflow configuration.",
        variant: "destructive",
      })
      return
    }

    try {
      await saveJobConfigMutation.mutateAsync({
        jobId,
        data: {
          jobId,
          flow: savedConfig.flow,
          stageConfig: savedConfig.stageConfig,
        }
      })

      toast({
        title: "Workflow Saved",
        description: "Your workflow has been saved successfully.",
      })
    } catch (error) {
      console.error('Error saving workflow:', error)
      toast({
        title: "Save Failed",
        description: "Failed to save workflow. Please try again.",
        variant: "destructive",
      })
    }
  }

  const selectedJob = jobs?.find(j => j._id === jobId)
  const validation = validateWorkflow()

  // Show loading state
  if (jobsLoading || configLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading workflow...</p>
        </div>
      </div>
    )
  }

  // Show error if no job ID provided
  if (!jobId) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              <span>No Job Selected</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">
              Please select a job from the workflows page to edit its workflow.
            </p>
            <Button onClick={() => navigate('/workflows')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Workflows
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => navigate('/workflows')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Workflows
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Workflow Editor</h1>
              <p className="text-gray-600">
                {selectedJob ? `Editing workflow for: ${selectedJob.title}` : 'Loading job details...'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">

            {/* Tab Navigation */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <Button
                variant={activeTab === 'editor' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('editor')}
              >
                Workflow Editor
              </Button>
              <Button
                variant={activeTab === 'manager' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('manager')}
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage Configs
              </Button>
            </div>

            {/* Validation Status - only show for editor tab */}
            {activeTab === 'editor' && (
              <Badge variant={validation.isValid ? "default" : "destructive"}>
                {validation.isValid ? 'Valid' : `${validation.errors.length} Issues`}
              </Badge>
            )}

            {/* Action Buttons - only show for editor tab */}
            {activeTab === 'editor' && (
              <>
                <Button variant="outline" onClick={handleNewWorkflow} disabled={!jobId}>
                  <Settings className="h-4 w-4 mr-2" />
                  New Workflow
                </Button>

                <Button onClick={handleSave} disabled={!jobId || nodes.length === 0}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Workflow
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {activeTab === 'editor' ? (
          // Workflow Editor Tab
          <>
            <div className="flex-1 relative">
              <WorkflowCanvas jobId={jobId!} />
            </div>
            {/* Stage Configuration Panel */}
            <StageConfigPanel />
          </>
        ) : (
          // Job Config Manager Tab
          <div className="flex-1 p-6">
            <JobConfigManager selectedJobId={jobId} />
          </div>
        )}
      </div>

      {/* Status Bar */}
      {jobId && (
        <div className="bg-white border-t border-gray-200 px-6 py-2">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-6">
              <span>Stages: {nodes.length}</span>
              <span>Connections: {edges.length}</span>
              <span>Configured: {nodes.filter(n => n.data.isConfigured).length}/{nodes.length}</span>
            </div>

            {!validation.isValid && (
              <div className="flex items-center space-x-2 text-red-600">
                <AlertTriangle className="h-4 w-4" />
                <span>{validation.errors.length} validation issue(s)</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Stage Configuration Panel */}
      <StageConfigPanel />
    </div>
  )
}
